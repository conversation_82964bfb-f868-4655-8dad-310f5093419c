{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: <PERSON> logger initialized',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/public/foodchain/conversatoins',
    statusCode: 500,
    responseTime: '16ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '176',
    timestamp: '2025-11-21T20:52:41.771Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:22:41:2241'
}
{
  message: 'Server error: 500',
  stack: 'Error: Server error: 500\n' +
    '    at ServerResponse.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/middlewares/security.js:193:18)\n' +
    '    at ServerResponse.emit (node:events:530:35)\n' +
    '    at onFinish (node:_http_outgoing:1082:10)\n' +
    '    at callback (node:internal/streams/writable:766:21)\n' +
    '    at afterWrite (node:internal/streams/writable:710:5)\n' +
    '    at afterWriteTick (node:internal/streams/writable:696:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  method: 'GET',
  url: '/api/v1/public/foodchain/conversatoins',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 500,
  duration: 16,
  timestamp: '2025-11-22 02:22:41:2241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/get_all_conversations?userId=null',
    statusCode: 304,
    responseTime: '3ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T20:52:47.783Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:22:47:2247'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/get_all_conversations?userId=null',
    statusCode: 304,
    responseTime: '6ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:06.983Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:06:236'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/user/login',
    statusCode: 200,
    responseTime: '106ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '790',
    timestamp: '2025-11-21T20:53:17.330Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:17:2317'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/get_all_conversations?userId=67d819a968f029236483e0e9',
    statusCode: 200,
    responseTime: '107ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:17.609Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:17:2317'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/get-theme/6899f5527403b47ea1df69a2',
    statusCode: 304,
    responseTime: '25ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:19.835Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:19:2319'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 200,
    responseTime: '55ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: '254',
    timestamp: '2025-11-21T20:53:19.842Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:19:2319'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/get-theme/6899f5527403b47ea1df69a2',
    statusCode: 304,
    responseTime: '20ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:19.858Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:19:2319'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '48ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:19.890Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:19:2319'
}
{
  message: "Can't find /api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116 on this server!",
  stack: undefined,
  url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: 'anonymous',
  timestamp: '2025-11-22 02:23:19:2319',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 404,
    responseTime: '3ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '197',
    timestamp: '2025-11-21T20:53:19.895Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:19:2319'
}
{
  message: "Can't find /api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116 on this server!",
  stack: undefined,
  url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: 'anonymous',
  timestamp: '2025-11-22 02:23:19:2319',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 404,
    responseTime: '2ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '197',
    timestamp: '2025-11-21T20:53:19.897Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:19:2319'
}
{
  message: 'Performance: cache_miss',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  timestamp: '2025-11-22 02:23:19:2319',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: cache_set',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  ttl: 300,
  timestamp: '2025-11-22 02:23:20:2320',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: getDishes_database_query',
  duration: '130ms',
  dishCount: 40,
  personalized: true,
  timestamp: '2025-11-22 02:23:20:2320',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/dishes?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116&personalize=true&userId=67d819a968f029236483e0e9',
    statusCode: 200,
    responseTime: '132ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:20.037Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:20:2320'
}
{
  message: 'Performance: cache_hit',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  timestamp: '2025-11-22 02:23:20:2320',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: getDishes_cache_hit',
  duration: '1ms',
  cacheKey: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  dishCount: 40,
  timestamp: '2025-11-22 02:23:20:2320',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/dishes?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116&personalize=true&userId=67d819a968f029236483e0e9',
    statusCode: 200,
    responseTime: '6ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:20.043Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:20:2320'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/get_conversation?userId=67d819a968f029236483e0e9&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 200,
    responseTime: '24ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:20.065Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:20:2320'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/get_conversation?userId=67d819a968f029236483e0e9&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '23ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:20.122Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:20:2320'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '44ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:20.513Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:20:2320'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/agentic-conversation?foodChainId=6899f5527403b47ea1df69a2&message=what+all+cuisines+you+serve&userId=67d819a968f029236483e0e9&outletId=6899fa5358dbcc8f5ced8116&language=en',
    statusCode: 200,
    responseTime: '1172ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:34.751Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:34:2334'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '44ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:49.830Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:49:2349'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/agentic-conversation?foodChainId=6899f5527403b47ea1df69a2&message=do+you+serve+momos&userId=67d819a968f029236483e0e9&outletId=6899fa5358dbcc8f5ced8116&language=en',
    statusCode: 200,
    responseTime: '814ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T20:53:52.870Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:23:52:2352'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '69ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:54:36.012Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:24:36:2436'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '47ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:54:53.374Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:24:53:2453'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '43ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:54:59.866Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:24:59:2459'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '46ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:55:13.407Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:25:13:2513'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '284ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:56:29.877Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:26:29:2629'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '43ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:56:47.638Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:26:47:2647'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '58ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:57:04.650Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:27:04:274'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '46ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:57:24.636Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:27:24:2724'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '44ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:57:27.333Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:27:27:2727'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '48ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:57:59.638Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:27:59:2759'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '45ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T20:58:11.636Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:28:11:2811'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '44ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:01:54.548Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:31:54:3154'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '50ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:01:54.627Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:31:54:3154'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/agentic-conversation?foodChainId=6899f5527403b47ea1df69a2&message=do+you+serve+momos&userId=67d819a968f029236483e0e9&outletId=6899fa5358dbcc8f5ced8116&language=en',
    statusCode: 200,
    responseTime: '1169ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:02:03.460Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:32:03:323'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '41ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:02:19.833Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:32:19:3219'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '40ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:02:49.832Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:32:49:3249'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '48ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:03:01.792Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:33:01:331'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '42ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:04:38.525Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:34:38:3438'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '47ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:05:50.636Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:35:50:3550'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '50ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:05:59.640Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:35:59:3559'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '48ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:06:10.636Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:36:10:3610'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '44ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:06:16.683Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:36:16:3616'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '43ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:06:16.755Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:36:16:3616'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '47ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:06:33.637Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:36:33:3633'
}
{
  message: "Can't find /api/recommendations on this server!",
  stack: undefined,
  url: '/api/recommendations',
  method: 'POST',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: 'anonymous',
  timestamp: '2025-11-22 02:38:39:3839',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'POST',
    url: '/api/recommendations',
    statusCode: 404,
    responseTime: '10ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: '116',
    timestamp: '2025-11-21T21:08:39.662Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:38:39:3839'
}
{
  message: "Can't find /api/recommendations/optimized?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3 on this server!",
  stack: undefined,
  url: '/api/recommendations/optimized?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3',
  method: 'GET',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: 'anonymous',
  timestamp: '2025-11-22 02:39:24:3924',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/recommendations/optimized?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3',
    statusCode: 401,
    responseTime: '4ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: '43',
    timestamp: '2025-11-21T21:09:42.939Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:39:42:3942'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/conversation?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3',
    statusCode: 200,
    responseTime: '532ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:10:28.991Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:40:28:4028'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '47ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:11:08.638Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:41:08:418'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/conversation?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3',
    statusCode: 200,
    responseTime: '1847ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:11:18.373Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:41:18:4118'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/conversation?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3',
    statusCode: 200,
    responseTime: '733ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:12:18.591Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:42:18:4218'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/conversation?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3',
    statusCode: 200,
    responseTime: '847ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:12:50.972Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:42:50:4250'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/conversation?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3',
    statusCode: 200,
    responseTime: '734ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:13:32.484Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:43:32:4332'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/conversation?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3',
    statusCode: 200,
    responseTime: '806ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:15:18.097Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:45:18:4518'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/conversation?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6899fa5358dbcc8f5ced8116&foodChainId=6899f5527403b47ea1df69a2',
    statusCode: 200,
    responseTime: '985ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:18:19.078Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:48:19:4819'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/conversation?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6899fa5358dbcc8f5ced8116&foodChainId=6899f5527403b47ea1df69a2',
    statusCode: 200,
    responseTime: '855ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:19:00.807Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:49:00:490'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/conversation?message=do%20you%20have%20noodles&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6899fa5358dbcc8f5ced8116&foodChainId=6899f5527403b47ea1df69a2',
    statusCode: 200,
    responseTime: '868ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:19:11.536Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:49:11:4911'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/conversation?message=what%20do%20you%20recommend&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6899fa5358dbcc8f5ced8116&foodChainId=6899f5527403b47ea1df69a2',
    statusCode: 200,
    responseTime: '1077ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:19:28.064Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:49:28:4928'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '67ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:25:26.138Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:55:26:5526'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/get-theme/6899f5527403b47ea1df69a2',
    statusCode: 304,
    responseTime: '22ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:25:27.281Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:55:27:5527'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/get-theme/6899f5527403b47ea1df69a2',
    statusCode: 304,
    responseTime: '24ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:25:27.311Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:55:27:5527'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '54ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:25:27.320Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:55:27:5527'
}
{
  message: "Can't find /api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116 on this server!",
  stack: undefined,
  url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: 'anonymous',
  timestamp: '2025-11-22 02:55:27:5527',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 404,
    responseTime: '3ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '197',
    timestamp: '2025-11-21T21:25:27.373Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:55:27:5527'
}
{
  message: 'Performance: cache_miss',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  timestamp: '2025-11-22 02:55:27:5527',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: cache_set',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  ttl: 300,
  timestamp: '2025-11-22 02:55:27:5527',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: getDishes_database_query',
  duration: '133ms',
  dishCount: 40,
  personalized: true,
  timestamp: '2025-11-22 02:55:27:5527',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/dishes?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116&personalize=true&userId=67d819a968f029236483e0e9',
    statusCode: 200,
    responseTime: '140ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:25:27.518Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:55:27:5527'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/get_conversation?userId=67d819a968f029236483e0e9&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 200,
    responseTime: '26ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:25:27.546Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:55:27:5527'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '46ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:25:28.010Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:55:28:5528'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/agentic-conversation?foodChainId=6899f5527403b47ea1df69a2&message=DO+YOU+HAVE+MOMOS&userId=67d819a968f029236483e0e9&outletId=6899fa5358dbcc8f5ced8116&language=en',
    statusCode: 200,
    responseTime: '1069ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:25:37.863Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:55:37:5537'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '63ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:25:45.096Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:55:45:5545'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/agentic-conversation?foodChainId=6899f5527403b47ea1df69a2&message=ANY+MORE+OPTIONS&userId=67d819a968f029236483e0e9&outletId=6899fa5358dbcc8f5ced8116&language=en',
    statusCode: 200,
    responseTime: '1106ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:25:50.368Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:55:50:5550'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '46ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:25:57.316Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:55:57:5557'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '50ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:26:52.363Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:56:52:5652'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '46ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:26:57.315Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:56:57:5657'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/agentic-conversation?foodChainId=6899f5527403b47ea1df69a2&message=do+you+have+noodles&userId=67d819a968f029236483e0e9&outletId=6899fa5358dbcc8f5ced8116&language=en',
    statusCode: 200,
    responseTime: '892ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:27:07.250Z'
  },
  level: 'http',
  timestamp: '2025-11-22 02:57:07:577'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '51ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:32:20.192Z'
  },
  level: 'http',
  timestamp: '2025-11-22 03:02:20:220'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '41ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:32:27.310Z'
  },
  level: 'http',
  timestamp: '2025-11-22 03:02:27:227'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/agentic-conversation?foodChainId=6899f5527403b47ea1df69a2&message=do+you+have+something+lesser+spicy+option&userId=67d819a968f029236483e0e9&outletId=6899fa5358dbcc8f5ced8116&language=en',
    statusCode: 200,
    responseTime: '1009ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-21T21:32:37.824Z'
  },
  level: 'http',
  timestamp: '2025-11-22 03:02:37:237'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '42ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-21T21:32:57.312Z'
  },
  level: 'http',
  timestamp: '2025-11-22 03:02:57:257'
}
{
  message: 'Received SIGINT. Starting graceful shutdown...',
  timestamp: '2025-11-22 03:03:02:32',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Could not close connections in time, forcefully shutting down',
  stack: 'Error: Could not close connections in time, forcefully shutting down\n' +
    '    at Timeout._onTimeout (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:215:14)\n' +
    '    at listOnTimeout (node:internal/timers:588:17)\n' +
    '    at process.processTimers (node:internal/timers:523:7)',
  timestamp: '2025-11-22 03:03:32:332',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
