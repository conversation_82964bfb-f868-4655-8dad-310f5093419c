{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Server error: 500',
  stack: 'Error: Server error: 500\n' +
    '    at ServerResponse.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/middlewares/security.js:193:18)\n' +
    '    at ServerResponse.emit (node:events:530:35)\n' +
    '    at onFinish (node:_http_outgoing:1082:10)\n' +
    '    at callback (node:internal/streams/writable:766:21)\n' +
    '    at afterWrite (node:internal/streams/writable:710:5)\n' +
    '    at afterWriteTick (node:internal/streams/writable:696:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  method: 'GET',
  url: '/api/v1/public/foodchain/conversatoins',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 500,
  duration: 16,
  timestamp: '2025-11-22 02:22:41:2241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Can't find /api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116 on this server!",
  stack: undefined,
  url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: 'anonymous',
  timestamp: '2025-11-22 02:23:19:2319',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Can't find /api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116 on this server!",
  stack: undefined,
  url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: 'anonymous',
  timestamp: '2025-11-22 02:23:19:2319',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:26:27:2627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:26:44:2644',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:27:00:270',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:27:21:2721',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:27:57:2757',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:28:08:288',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:35:47:3547',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:35:57:3557',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:36:07:367',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:36:29:3629',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Can't find /api/recommendations on this server!",
  stack: undefined,
  url: '/api/recommendations',
  method: 'POST',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: 'anonymous',
  timestamp: '2025-11-22 02:38:39:3839',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Can't find /api/recommendations/optimized?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3 on this server!",
  stack: undefined,
  url: '/api/recommendations/optimized?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3',
  method: 'GET',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: 'anonymous',
  timestamp: '2025-11-22 02:39:24:3924',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:41:06:416',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:42:08:428',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:42:41:4241',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:43:21:4321',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:46:27:4627',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:46:50:4650',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:47:08:478',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:47:33:4733',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:47:58:4758',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:48:47:4847',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:49:20:4920',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Can't find /api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116 on this server!",
  stack: undefined,
  url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: 'anonymous',
  timestamp: '2025-11-22 02:55:27:5527',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Could not close connections in time, forcefully shutting down',
  stack: 'Error: Could not close connections in time, forcefully shutting down\n' +
    '    at Timeout._onTimeout (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:215:14)\n' +
    '    at listOnTimeout (node:internal/timers:588:17)\n' +
    '    at process.processTimers (node:internal/timers:523:7)',
  timestamp: '2025-11-22 03:03:32:332',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
