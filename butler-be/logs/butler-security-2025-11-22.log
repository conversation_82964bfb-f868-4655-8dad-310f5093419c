{
  message: {
    event: 'endpoint_not_found',
    url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    method: 'GET',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    timestamp: '2025-11-21T20:53:19.894Z'
  },
  level: 'warn',
  timestamp: '2025-11-22 02:23:19:2319'
}
{
  message: {
    event: 'endpoint_not_found',
    url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    method: 'GET',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    timestamp: '2025-11-21T20:53:19.896Z'
  },
  level: 'warn',
  timestamp: '2025-11-22 02:23:19:2319'
}
{
  message: {
    event: 'endpoint_not_found',
    url: '/api/recommendations',
    method: 'POST',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    timestamp: '2025-11-21T21:08:39.661Z'
  },
  level: 'warn',
  timestamp: '2025-11-22 02:38:39:3839'
}
{
  message: {
    event: 'endpoint_not_found',
    url: '/api/recommendations/optimized?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3',
    method: 'GET',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    timestamp: '2025-11-21T21:09:24.704Z'
  },
  level: 'warn',
  timestamp: '2025-11-22 02:39:24:3924'
}
{
  message: {
    event: 'unauthorized_access_attempt',
    method: 'GET',
    url: '/api/v1/recommendations/optimized?message=do%20you%20serve%20momos&userId=6751b3b4e5b4c2a1b8d9e0f1&outletId=6751b3b4e5b4c2a1b8d9e0f2&foodChainId=6751b3b4e5b4c2a1b8d9e0f3',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    statusCode: 401,
    timestamp: '2025-11-21T21:09:42.940Z'
  },
  level: 'warn',
  timestamp: '2025-11-22 02:39:42:3942'
}
{
  message: {
    event: 'endpoint_not_found',
    url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    method: 'GET',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    timestamp: '2025-11-21T21:25:27.372Z'
  },
  level: 'warn',
  timestamp: '2025-11-22 02:55:27:5527'
}
